<?php
session_start();
include '../config.php';
include '../includes/recaptcha.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

$message = '';
$error_message = '';

// جلب إعدادات reCAPTCHA
$recaptcha_settings = getRecaptchaSettings($pdo);

if ($_POST) {
    if (!validateRecaptchaInForm($pdo)) {
        $error_message = 'فشل التحقق من reCAPTCHA';
    } else {
        $message = 'تم التحقق من reCAPTCHA بنجاح!';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار reCAPTCHA - لوحة التحكم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    
    <?php echo getRecaptchaScript($recaptcha_settings['enabled']); ?>
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 2rem 0;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .g-recaptcha {
            display: flex;
            justify-content: center;
            margin: 1.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h2 class="text-center mb-4">
                <i class="fas fa-shield-alt me-2"></i>
                اختبار Google reCAPTCHA
            </h2>
            
            <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
            </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error_message; ?>
            </div>
            <?php endif; ?>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">حالة إعدادات reCAPTCHA</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>الحالة:</strong>
                            <?php if ($recaptcha_settings['enabled']): ?>
                                <span class="badge bg-success">مفعل</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">غير مفعل</span>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <strong>Site Key:</strong>
                            <?php if (!empty($recaptcha_settings['site_key'])): ?>
                                <span class="badge bg-success">موجود</span>
                            <?php else: ?>
                                <span class="badge bg-danger">غير موجود</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <strong>Secret Key:</strong>
                            <?php if (!empty($recaptcha_settings['secret_key'])): ?>
                                <span class="badge bg-success">موجود</span>
                            <?php else: ?>
                                <span class="badge bg-danger">غير موجود</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if ($recaptcha_settings['enabled'] && !empty($recaptcha_settings['site_key'])): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">اختبار reCAPTCHA</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <p>قم بحل reCAPTCHA أدناه واضغط على "اختبار" للتحقق من عمل النظام:</p>
                        
                        <?php echo renderRecaptcha($recaptcha_settings['site_key'], $recaptcha_settings['enabled']); ?>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-test-tube me-2"></i>
                                اختبار reCAPTCHA
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <?php else: ?>
            <div class="alert alert-warning mt-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> يجب تفعيل reCAPTCHA وإدخال المفاتيح في صفحة الإعدادات أولاً.
            </div>
            <?php endif; ?>
            
            <div class="text-center mt-4">
                <a href="settings.php" class="btn btn-secondary me-2">
                    <i class="fas fa-cog me-2"></i>
                    إعدادات reCAPTCHA
                </a>
                <a href="dashboard.php" class="btn btn-outline-primary">
                    <i class="fas fa-home me-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
